# System Configuration Update Script
# This script performs two main tasks:
# 1. Check and update TimeOut value to 300 in SystemParam.xml files
# 2. Copy all files from 88123 folder to IBS root directory

param(
    [string]$WorkspaceRoot = ".",
    [string]$SourceFolder = "88123",
    [string]$TargetFolder = "IBS",
    [int]$RequiredTimeOut = 300
)

# Function to log messages with timestamp
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message"
}

# Function to update TimeOut value in XML file
function Update-TimeOutInXML {
    param(
        [string]$FilePath,
        [int]$NewTimeOut
    )
    
    try {
        if (-not (Test-Path $FilePath)) {
            Write-Log "File not found: $FilePath" "WARNING"
            return $false
        }
        
        # Read the XML content as text to preserve formatting
        $content = Get-Content $FilePath -Raw
        
        # Check if TimeOut attribute exists
        if ($content -match 'TimeOut="(\d+)"') {
            $currentTimeOut = $matches[1]
            Write-Log "Current TimeOut value in ${FilePath}: $currentTimeOut"
            
            if ($currentTimeOut -ne $NewTimeOut) {
                # Replace the TimeOut value
                $updatedContent = $content -replace 'TimeOut="\d+"', "TimeOut=`"$NewTimeOut`""
                
                # Write back to file
                Set-Content -Path $FilePath -Value $updatedContent -Encoding UTF8
                Write-Log "Updated TimeOut from $currentTimeOut to $NewTimeOut in ${FilePath}" "SUCCESS"
                return $true
            } else {
                Write-Log "TimeOut value is already $NewTimeOut in ${FilePath}" "INFO"
                return $false
            }
        } else {
            Write-Log "TimeOut attribute not found in ${FilePath}" "WARNING"
            return $false
        }
    }
    catch {
        Write-Log "Error updating ${FilePath} : $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to copy files from source to target directory
function Copy-FilesToTarget {
    param(
        [string]$SourcePath,
        [string]$TargetPath
    )
    
    try {
        if (-not (Test-Path $SourcePath)) {
            Write-Log "Source folder not found: $SourcePath" "ERROR"
            return $false
        }
        
        if (-not (Test-Path $TargetPath)) {
            Write-Log "Target folder not found: $TargetPath" "ERROR"
            return $false
        }
        
        # Get all files in source directory
        $sourceFiles = Get-ChildItem -Path $SourcePath -File -Recurse
        
        if ($sourceFiles.Count -eq 0) {
            Write-Log "No files found in source folder: $SourcePath" "WARNING"
            return $false
        }
        
        Write-Log "Found $($sourceFiles.Count) files to copy from $SourcePath"
        
        foreach ($file in $sourceFiles) {
            # Get just the filename for direct copy to target root
            $fileName = $file.Name
            $targetFilePath = Join-Path $TargetPath $fileName

            # Copy file with overwrite directly to target root
            Copy-Item -Path $file.FullName -Destination $targetFilePath -Force
            Write-Log "Copied: $fileName to target root directory"
        }
        
        Write-Log "Successfully copied all files from $SourcePath to $TargetPath" "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Error copying files: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Main execution
Write-Log "Starting System Configuration Update Script"
Write-Log "Workspace Root: $WorkspaceRoot"
Write-Log "Source Folder: $SourceFolder"
Write-Log "Target Folder: $TargetFolder"
Write-Log "Required TimeOut: $RequiredTimeOut"

# Set working directory
Set-Location $WorkspaceRoot

# Task 1: Update TimeOut values in SystemParam.xml files
Write-Log "=== Task 1: Checking and updating TimeOut values ==="

$xmlFiles = @(
    "IBS\Config\ThreePhase\SystemParam.xml",
    "IBS\Config\SinglePhase\SystemParam.xml",
    "IBS\Config\Terminal\SystemParam.xml",
    "IBS\Config\XML\SystemParam.xml"
)

$updatedCount = 0
foreach ($xmlFile in $xmlFiles) {
    if (Update-TimeOutInXML -FilePath $xmlFile -NewTimeOut $RequiredTimeOut) {
        $updatedCount++
    }
}

Write-Log "Updated TimeOut value in $updatedCount file(s)"

# Task 2: Copy files from 88123 to IBS
Write-Log "=== Task 2: Copying files from $SourceFolder to $TargetFolder ==="

$sourcePath = Join-Path $WorkspaceRoot $SourceFolder
$targetPath = Join-Path $WorkspaceRoot $TargetFolder

$copyResult = Copy-FilesToTarget -SourcePath $sourcePath -TargetPath $targetPath

# Summary
Write-Log "=== Script Execution Summary ==="
if ($updatedCount -gt 0) {
    Write-Log "TimeOut update: $updatedCount file(s) updated successfully" "SUCCESS"
} else {
    Write-Log "TimeOut update: No files needed updating" "INFO"
}

if ($copyResult) {
    Write-Log "File copy: All files copied successfully" "SUCCESS"
} else {
    Write-Log "File copy: Failed to copy files" "ERROR"
}

Write-Log "Script execution completed"

# Pause to allow user to see results
Read-Host "Press Enter to exit"
