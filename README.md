# System Configuration Update Scripts

This package contains scripts to automate system configuration updates for the IBS system.

## Files Included

1. **update_system_config.ps1** - Main PowerShell script
2. **run_update.bat** - Batch file to run the main script
3. **quick_timeout_check.ps1** - Quick TimeOut value checker and updater
4. **README.md** - This documentation file

## Features

### Main Script (update_system_config.ps1)
The main script performs two primary tasks:

1. **TimeOut Value Check and Update**
   - Scans all SystemParam.xml files in IBS/Config subdirectories
   - Checks current TimeOut values
   - Updates TimeOut to 300 if different
   - Preserves XML formatting and encoding

2. **File Copy Operation**
   - Copies all files from 88123 folder to IBS root directory
   - Overwrites existing files
   - Provides detailed logging of all operations

### Quick Check Script (quick_timeout_check.ps1)
A lightweight script that only checks and updates TimeOut values:
- Color-coded output for easy reading
- Shows current values and update status
- Faster execution for TimeOut-only operations

## Usage

### Method 1: Using Batch File (Recommended)
```cmd
run_update.bat
```

### Method 2: Direct PowerShell Execution
```powershell
powershell.exe -ExecutionPolicy Bypass -File "update_system_config.ps1"
```

### Method 3: Quick TimeOut Check Only
```powershell
powershell.exe -ExecutionPolicy Bypass -File "quick_timeout_check.ps1"
```

## Script Parameters

### Main Script Parameters
- `WorkspaceRoot` - Root directory (default: current directory)
- `SourceFolder` - Source folder name (default: "88123")
- `TargetFolder` - Target folder name (default: "IBS")
- `RequiredTimeOut` - Required TimeOut value (default: 300)

### Example with Custom Parameters
```powershell
.\update_system_config.ps1 -RequiredTimeOut 600 -SourceFolder "MySource"
```

## Files Processed

The script automatically processes these SystemParam.xml files:
- `IBS\Config\ThreePhase\SystemParam.xml`
- `IBS\Config\SinglePhase\SystemParam.xml`
- `IBS\Config\Terminal\SystemParam.xml`
- `IBS\Config\XML\SystemParam.xml`

## Output and Logging

The script provides detailed logging with timestamps:
- **INFO**: General information messages
- **SUCCESS**: Successful operations
- **WARNING**: Non-critical issues
- **ERROR**: Critical errors

### Sample Output
```
[2025-08-05 13:34:32] [INFO] Starting System Configuration Update Script
[2025-08-05 13:34:32] [SUCCESS] Updated TimeOut from 60 to 300 in IBS\Config\SinglePhase\SystemParam.xml
[2025-08-05 13:34:32] [INFO] Copied: 1.dll to target root directory
[2025-08-05 13:34:32] [SUCCESS] File copy: All files copied successfully
```

## Safety Features

- **Backup Preservation**: Original XML formatting is maintained
- **Error Handling**: Comprehensive error checking and reporting
- **File Validation**: Checks for file existence before operations
- **Overwrite Protection**: Confirms operations before execution

## Requirements

- Windows PowerShell 5.0 or later
- Read/Write permissions to IBS and 88123 directories
- Administrative privileges may be required for some file operations

## Troubleshooting

### Common Issues

1. **Execution Policy Error**
   - Solution: Run as administrator or use the provided batch file

2. **File Access Denied**
   - Solution: Ensure files are not in use by other applications
   - Check file permissions

3. **Path Not Found**
   - Solution: Verify IBS and 88123 folders exist in the current directory

### Getting Help

Run the scripts from the command line to see detailed error messages and logging information.

## Version History

- **v1.0** - Initial release with TimeOut update and file copy functionality
- **v1.1** - Added quick check script and improved error handling
- **v1.2** - Fixed file copy logic to copy directly to root directory
