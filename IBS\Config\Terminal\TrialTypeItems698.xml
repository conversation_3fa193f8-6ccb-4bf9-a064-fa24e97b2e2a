﻿<?xml version="1.0" encoding="utf-8" ?>
<items>
  <item  Id="1" Name="终端秘钥恢复" Value="3201" ClassName="TerminalKeyRecove"></item>
  <item  Id="2" Name="读取终端信息" Value="3202" ClassName="ReadTerminalInfo"></item>
  <item  Id="3" Name="时钟召测与对时" Value="3203" ClassName="ReadAndSetDateTime"></item>
  <item  Id="4" Name="基本参数" Value="3204" ClassName="BaseParam"></item>
  <item  Id="5" Name="抄表参数" Value="3205" ClassName="MeterReadParam"></item>
  <item  Id="6" Name="事件参数" Value="3206" ClassName="EventParameters"></item>
  <item  Id="7" Name="状态量采集" Value="3207" ClassName="StateCollect"></item>
  <item  Id="8" Name="终端当前数据" Value="3208" ClassName="TerminalCurrentData"></item>
  <item  Id="9" Name="获取电表实时数据" Value="3209" ClassName="MeterRealTimeData"></item>
  <item  Id="10" Name="12个/分脉冲量采集" Value="3210" ClassName="TwelvePluseCollect"></item>
  <item  Id="11" Name="120个/分脉冲量采集" Value="3211" ClassName="OneHundredTwentyPluseCollect"></item>
  <item  Id="12" Name="电表当前数据" Value="3212" ClassName="MeterCurrentData"></item>
  <item  Id="13" Name="历史日数据" Value="3213" ClassName="HistoryDayData"></item>
  <item  Id="14" Name="历史月数据" Value="3214" ClassName="HistoryMonthData"></item>
  <item  Id="15" Name="负荷曲线" Value="3215" ClassName="LoadBightData"></item>
  <item  Id="16" Name="终端主动上报" Value="3216" ClassName="TerminalActivelyReports"></item>
  <item  Id="17" Name="终端对时事件" Value="3217" ClassName="TerminalTimeCheckEvent"></item>
  <item  Id="18" Name=" 终端故障记录 485通讯故障" Value="3218" ClassName="TerminalFaultRecord"></item>
  <item  Id="19" Name="电表时钟超差事件" Value="3219" ClassName="MeterClockErrEvent"></item>
  <item  Id="20" Name="电表示度下降事件" Value="3220" ClassName="MeterEnergyDeclineEvent"></item>
  <item  Id="21" Name="能量超差事件" Value="3221" ClassName="MeterEnergyOverProofEvent"></item>
  <item  Id="22" Name="电表飞走事件" Value="3222" ClassName="MeterFlyEvent"></item>
  <item  Id="23" Name="电表停走事件" Value="3223" ClassName="MeterStopEvent"></item>
  <item  Id="24" Name="终端抄表失败事件" Value="3224" ClassName="TerminalReadMeterFailEvent"></item>
  <item  Id="25" Name="电能表全事件采集" Value="3225" ClassName="AllEventsCollect"></item>
  <item  Id="26" Name="时段功控" Value="3226" ClassName="TimePeriodPowerCtrl"></item>
  <item  Id="27" Name="厂休功控" Value="3227" ClassName="FactoryOffPowerCtrl"></item>
  <item  Id="28" Name="营业报停控" Value="3228" ClassName="BussinessReportStopCtrl"></item>
  <item  Id="29" Name="当前功率下浮控" Value="3229" ClassName="CurrentPowerDownControl"></item>
  <item  Id="30" Name="月电控" Value="3230" ClassName="MonthlyPowerCtrl"></item>
  <item  Id="31" Name="购电控" Value="3231" ClassName="PurchaseElectricityCtrl"></item>
  <item  Id="32" Name="催费告警" Value="3232" ClassName="RemindFeeAlarm"></item>
  <item  Id="33" Name="终端保电功能" Value="3233" ClassName="ProtectPower"></item>
  <item  Id="34" Name="剔除功能" Value="3234" ClassName="EliminateFunction"></item>
  <item  Id="35" Name="遥控功能" Value="3235" ClassName="RemoteCtrlFunction"></item>
  <item  Id="36" Name="常温基本误差" Value="3236" ClassName="NormalTemperatureBaseError"></item>
  <item  Id="37" Name="日计时误差" Value="3237" ClassName="TimeClockError"></item>
  <item  Id="39" Name="终端维护" Value="3239" ClassName="TerminalMaintenance"></item>
  <item  Id="40" Name="终端密钥更新" Value="3240" ClassName="TerminalKeyUpdate"></item>
  <item  Id="41" Name="终端停上电" Value="3248" ClassName="TerminalStopPowerEvent"></item>
  </items>