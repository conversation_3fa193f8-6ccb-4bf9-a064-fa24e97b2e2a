@echo off
echo System Configuration Update Script
echo ==================================
echo.
echo This script will:
echo 1. Check and update TimeOut values to 300 in SystemParam.xml files
echo 2. Copy all files from 88123 folder to IBS root directory
echo.
pause

REM Run PowerShell script with execution policy bypass
powershell.exe -ExecutionPolicy Bypass -File "update_system_config.ps1"

echo.
echo Script execution completed.
pause
