# Quick TimeOut Check and Update Script
# This script quickly checks and updates TimeOut values in SystemParam.xml files

param(
    [int]$RequiredTimeOut = 300
)

function Check-And-Update-TimeOut {
    param(
        [string]$FilePath,
        [int]$NewTimeOut
    )
    
    if (-not (Test-Path $FilePath)) {
        Write-Host "File not found: $FilePath" -ForegroundColor Yellow
        return
    }
    
    $content = Get-Content $FilePath -Raw
    
    if ($content -match 'TimeOut="(\d+)"') {
        $currentTimeOut = $matches[1]
        Write-Host "File: $FilePath" -ForegroundColor Cyan
        Write-Host "  Current TimeOut: $currentTimeOut" -ForegroundColor White
        
        if ($currentTimeOut -ne $NewTimeOut) {
            $updatedContent = $content -replace 'TimeOut="\d+"', "TimeOut=`"$NewTimeOut`""
            Set-Content -Path $FilePath -Value $updatedContent -Encoding UTF8
            Write-Host "  Status: UPDATED to $NewTimeOut" -ForegroundColor Green
        } else {
            Write-Host "  Status: Already correct ($NewTimeOut)" -ForegroundColor Green
        }
    } else {
        Write-Host "File: $FilePath" -ForegroundColor Cyan
        Write-Host "  Status: TimeOut attribute not found" -ForegroundColor Red
    }
    Write-Host ""
}

Write-Host "Quick TimeOut Check and Update Tool" -ForegroundColor Magenta
Write-Host "===================================" -ForegroundColor Magenta
Write-Host "Required TimeOut value: $RequiredTimeOut" -ForegroundColor Yellow
Write-Host ""

$xmlFiles = @(
    "IBS\Config\ThreePhase\SystemParam.xml",
    "IBS\Config\SinglePhase\SystemParam.xml", 
    "IBS\Config\Terminal\SystemParam.xml",
    "IBS\Config\XML\SystemParam.xml"
)

foreach ($xmlFile in $xmlFiles) {
    Check-And-Update-TimeOut -FilePath $xmlFile -NewTimeOut $RequiredTimeOut
}

Write-Host "Check completed!" -ForegroundColor Magenta
Read-Host "Press Enter to exit"
